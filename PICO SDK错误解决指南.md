# PICO SDK错误解决指南

## 快速诊断步骤

### 1. 使用诊断工具
1. 在场景中添加`PICOConfigurationChecker`组件
2. 右键点击组件 → "检查PICO配置"
3. 查看Console输出的详细信息

### 2. 检查Unity Console
打开`Window → Console`，查看是否有：
- 红色错误信息（编译错误）
- 黄色警告信息（配置警告）

## 常见错误类型及解决方案

### 错误1：编译错误 - 类型找不到
**错误信息示例：**
```
error CS0246: The type or namespace name 'PXR_Manager' could not be found
error CS0246: The type or namespace name 'XRRayInteractor' could not be found
```

**解决方案：**
1. **重新导入PICO SDK**
   ```
   - 删除 Packages/com.unity.xr.picoxr 文件夹
   - 重新导入PICO Unity Integration SDK
   - 等待Unity重新编译
   ```

2. **检查包依赖**
   ```
   Window → Package Manager → In Project
   确保以下包已安装：
   - XR Plugin Management (4.0.0+)
   - XR Interaction Toolkit (2.0.0+)
   - PICO Integration (3.2.0+)
   ```

### 错误2：XR设置未配置
**症状：** VR模式无法启动，头显黑屏

**解决方案：**
1. **配置XR插件管理器**
   ```
   Edit → Project Settings → XR Plug-in Management
   - 勾选 "Initialize XR on Startup"
   - 切换到 Android 标签页
   - 勾选 "PICO" 插件
   ```

2. **验证配置**
   ```
   - 重启Unity编辑器
   - 检查Project Settings中是否显示PICO设置页面
   ```

### 错误3：Android构建设置问题
**症状：** 构建失败或APK无法在PICO设备上运行

**解决方案：**
1. **调整Android设置**
   ```
   Edit → Project Settings → Player → Android Settings
   
   必要设置：
   - Minimum API Level: Android 7.0 (API Level 24)
   - Target API Level: Android 10.0 (API Level 29) 或更高
   - Scripting Backend: IL2CPP
   - Target Architectures: ARM64 ✓
   - Graphics APIs: OpenGLES3, Vulkan
   ```

2. **检查权限设置**
   ```
   确保以下权限已添加：
   - Camera (如果使用AR功能)
   - Microphone (如果使用语音功能)
   ```

### 错误4：场景配置问题
**症状：** VR功能无响应，控制器不工作

**解决方案：**
1. **添加XR Origin**
   ```
   - 删除原有的Main Camera
   - GameObject → XR → XR Origin (VR)
   - 确保XR Origin包含左右控制器
   ```

2. **配置VR组件**
   ```
   确保场景中有：
   - VRAssemblyManager
   - VRInputManager
   - Neo4jAssemblyController (enableVRMode = true)
   ```

### 错误5：版本兼容性问题
**症状：** 导入后出现大量编译错误

**解决方案：**
1. **检查版本兼容性**
   ```
   Unity 2021.3.44f1 兼容的PICO SDK版本：
   - PICO Unity Integration SDK 3.2.0+
   - XR Plugin Management 4.4.0
   - XR Interaction Toolkit 2.5.2
   ```

2. **降级或升级包版本**
   ```
   Window → Package Manager
   - 选择有问题的包
   - 点击版本下拉菜单
   - 选择兼容的版本
   ```

## 高级故障排除

### 1. 清理项目缓存
```
1. 关闭Unity
2. 删除以下文件夹：
   - Library/
   - Temp/
   - obj/
3. 重新打开Unity项目
```

### 2. 重新生成项目文件
```
1. Edit → Preferences → External Tools
2. 点击 "Regenerate project files"
3. 重启Unity和IDE
```

### 3. 检查PICO设备连接
```
1. 启用PICO设备开发者模式
2. 通过USB连接设备
3. 运行 adb devices 确认设备连接
4. 在Unity中测试 Build and Run
```

## 验证修复结果

### 1. 编译测试
```
- Console中无红色错误
- 项目可以正常构建
- 所有脚本编译通过
```

### 2. 功能测试
```
1. 添加PICOTestManager到场景
2. 运行项目
3. 检查Console输出的测试结果
4. 验证VR功能是否正常
```

### 3. 构建测试
```
1. File → Build Settings
2. 选择Android平台
3. 点击Build测试APK生成
4. 部署到PICO设备测试
```

## 预防措施

### 1. 版本管理
```
- 记录当前工作的包版本组合
- 避免随意更新包版本
- 使用版本控制系统跟踪变更
```

### 2. 备份策略
```
- 在导入PICO SDK前备份项目
- 保存工作的项目配置
- 记录成功的设置参数
```

### 3. 渐进式集成
```
- 先在新项目中测试PICO SDK
- 确认无问题后再集成到主项目
- 分步骤验证每个功能模块
```

## 联系支持

如果以上方法都无法解决问题：

1. **收集错误信息**
   - Console完整错误日志
   - Unity版本和PICO SDK版本
   - 项目配置截图

2. **使用诊断工具**
   - 运行PICOConfigurationChecker
   - 导出完整的诊断报告

3. **寻求帮助**
   - PICO开发者社区
   - Unity官方论坛
   - 技术支持渠道
