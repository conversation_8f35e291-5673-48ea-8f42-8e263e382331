Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker0.log
-srvPort
10633
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24856] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2567058274 [EditorId] 2567058274 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [24856] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2567058274 [EditorId] 2567058274 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 36.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56860
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.017631 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1040 ms
Refreshing native plugins compatible for Editor in 45.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.640 seconds
Domain Reload Profiling:
	ReloadAssembly (1640ms)
		BeginReloadAssembly (131ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1391ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (72ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (1244ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1096ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (45ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (68ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.009565 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 40.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.305 seconds
Domain Reload Profiling:
	ReloadAssembly (1306ms)
		BeginReloadAssembly (123ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1091ms)
			LoadAssemblies (111ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (187ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (749ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (66ms)
				ProcessInitializeOnLoadAttributes (592ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (24ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4455 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (119.6 KB). Loaded Objects now: 4936.
Memory consumption went from 181.0 MB to 180.8 MB.
Total: 3.502100 ms (FindLiveObjects: 0.351700 ms CreateObjectMapping: 0.103500 ms MarkObjects: 2.942100 ms  DeleteObjects: 0.103700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 64000.295406 seconds.
  path: Assets/Scripts/VRAssemblyStepManager.cs
  artifactKey: Guid(e7e837cda683d6f40b9685cae6dbcaf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/VRAssemblyStepManager.cs using Guid(e7e837cda683d6f40b9685cae6dbcaf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e3d1902b799d684afd7a3a1b5345adde') in 0.031070 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013005 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 37.54 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 2.28 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bf756743 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bf75641b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bf7561a0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bf756068 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000203bf753113 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000203bf6c2e35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000203bf6c24da (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000203bf6c23eb (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000203bf6c1b33 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000203bb236298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000203c34f76b5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000203c34f6ed3 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000203c34f6b03 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000203bf6c2e83 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000201e8e01310 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000203bf75fdbb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000203bb22ff4e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000203c34e4b4b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bb236b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bf756743 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bf75641b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bf7561a0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203c34f7945 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000203c34e4b6b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bb236b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.799 seconds
Domain Reload Profiling:
	ReloadAssembly (1800ms)
		BeginReloadAssembly (186ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (1510ms)
			LoadAssemblies (140ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (198ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (1152ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (59ms)
				ProcessInitializeOnLoadAttributes (929ms)
				ProcessInitializeOnLoadMethodAttributes (68ms)
				AfterProcessingInitializeOnLoad (40ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.68 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 4732 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 192.4 MB to 191.2 MB.
Total: 3.295200 ms (FindLiveObjects: 0.198800 ms CreateObjectMapping: 0.061000 ms MarkObjects: 2.675400 ms  DeleteObjects: 0.358600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.32 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 98.8 MB to 97.5 MB.
Total: 2.540800 ms (FindLiveObjects: 0.212500 ms CreateObjectMapping: 0.062300 ms MarkObjects: 1.866700 ms  DeleteObjects: 0.398000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 14.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 98.4 MB to 97.2 MB.
Total: 2.496700 ms (FindLiveObjects: 0.220000 ms CreateObjectMapping: 0.082600 ms MarkObjects: 1.849600 ms  DeleteObjects: 0.343300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 451.682332 seconds.
  path: Assets/Resources/PXR_ProjectSetting.asset
  artifactKey: Guid(5ad2fb3f450ece946ae5c8bdf47b053a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Resources/PXR_ProjectSetting.asset using Guid(5ad2fb3f450ece946ae5c8bdf47b053a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a030810c64d8f2ef9fe801b559b3056') in 0.015134 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015165 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203c34cc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203c34cbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203c34cbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203c34cb9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000203c34c8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000203c1837575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000203c1836c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000203c1836b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000203c1836273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000203bae36298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000203bcd3d235 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000203bcd3ce93 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000203bcd3cac3 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000203c18375c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000203c34f6900 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000203c34f53db (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000203c34f529e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000203bcd2933b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bae36b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203c34cc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203c34cbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203c34cbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bcd3d4c5 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000203bcd2935b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bae36b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.328 seconds
Domain Reload Profiling:
	ReloadAssembly (1329ms)
		BeginReloadAssembly (178ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (1046ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (193ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (27ms)
			SetupLoadedEditorAssemblies (697ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (63ms)
				ProcessInitializeOnLoadAttributes (558ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (38ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 70 unused Assets / (1.2 MB). Loaded Objects now: 5221.
Memory consumption went from 192.1 MB to 190.8 MB.
Total: 2.136000 ms (FindLiveObjects: 0.168100 ms CreateObjectMapping: 0.058500 ms MarkObjects: 1.572400 ms  DeleteObjects: 0.336100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1293.168930 seconds.
  path: Assets/Scripts/PICOTestManager.cs
  artifactKey: Guid(15e83a2f88007d246970da28f795c3c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/PICOTestManager.cs using Guid(15e83a2f88007d246970da28f795c3c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ada14c0cd45b2535d02f833e2c75a791') in 0.010423 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 8.291459 seconds.
  path: Assets/Scripts/PICOConfigurationChecker.cs
  artifactKey: Guid(a15c99a3c995b9148ab736b29d7ccdd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/PICOConfigurationChecker.cs using Guid(a15c99a3c995b9148ab736b29d7ccdd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a3a38192061c77c72c39d384cd47186d') in 0.001264 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 10.629687 seconds.
  path: Assets/Scripts/VRSimulator.cs
  artifactKey: Guid(83472973bbfa55f4f9f26771438f6c04) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/VRSimulator.cs using Guid(83472973bbfa55f4f9f26771438f6c04) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c37b0d0ea9490541dcb6d651dee61d94') in 0.001577 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4.051782 seconds.
  path: Assets/Scripts/VRAssemblyDebugger.cs
  artifactKey: Guid(e3306149169973a43b24106b79afbd19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/VRAssemblyDebugger.cs using Guid(e3306149169973a43b24106b79afbd19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '92f2420b948947e86a35a177e1683b0f') in 0.001283 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 137.636506 seconds.
  path: Assets/Scripts/VRAssemblyPositioner.cs
  artifactKey: Guid(de9aa82b30f150e41ba7e964413ec99e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/VRAssemblyPositioner.cs using Guid(de9aa82b30f150e41ba7e964413ec99e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'df720b229869cfe1aa656fec2a936415') in 0.001265 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014154 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bcc8c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bcc8bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bcc8bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bcc8b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000203bcc88a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000203c1097575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000203c1096c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000203c1096b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000203c1096273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000203bf6e6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000203bce3caf5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000203bce3c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000203bce3c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000203c10975c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000203bccb7e90 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000203bccb696b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000203bccb682e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000203bce2905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bf6e6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bcc8c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bcc8bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bcc8bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bce3cd85 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000203bce2907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bf6e6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.296 seconds
Domain Reload Profiling:
	ReloadAssembly (1297ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1033ms)
			LoadAssemblies (128ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (186ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (689ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (65ms)
				ProcessInitializeOnLoadAttributes (550ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.59 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4722 Unused Serialized files (Serialized files now loaded: 0)
Unloading 74 unused Assets / (1.2 MB). Loaded Objects now: 5234.
Memory consumption went from 192.1 MB to 190.8 MB.
Total: 2.444200 ms (FindLiveObjects: 0.226200 ms CreateObjectMapping: 0.060500 ms MarkObjects: 1.808700 ms  DeleteObjects: 0.347300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 14.87 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5234.
Memory consumption went from 98.9 MB to 97.6 MB.
Total: 2.900700 ms (FindLiveObjects: 0.353700 ms CreateObjectMapping: 0.102800 ms MarkObjects: 2.063000 ms  DeleteObjects: 0.380600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014925 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.57 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bce0c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bce0bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bce0bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bce0b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000203bce08a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000203bcda7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000203bcda6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000203bcda6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000203bcda6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000203bcce6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000203bcd1caf5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000203bcd1c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000203bcd1c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000203bcda75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000203bce57e90 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000203bce5696b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000203bce5682e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000203bcd0905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bcce6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000203bce0c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000203bce0bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000203bce0bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000203bcd1cd85 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000203bcd0907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000203bcce6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.312 seconds
Domain Reload Profiling:
	ReloadAssembly (1313ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (1039ms)
			LoadAssemblies (136ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (27ms)
			SetupLoadedEditorAssemblies (695ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (64ms)
				ProcessInitializeOnLoadAttributes (557ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.56 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4722 Unused Serialized files (Serialized files now loaded: 0)
Unloading 70 unused Assets / (1.2 MB). Loaded Objects now: 5251.
Memory consumption went from 192.1 MB to 190.9 MB.
Total: 2.502000 ms (FindLiveObjects: 0.205700 ms CreateObjectMapping: 0.078400 ms MarkObjects: 1.878600 ms  DeleteObjects: 0.338500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
