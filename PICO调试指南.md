# PICO设备调试指南

## 问题诊断

您的情况：
- ✅ 能看到场景
- ✅ 射线跟随手柄移动
- ❌ 按键没有响应

这说明VR基础配置正确，问题在于输入检测。

## 解决方案

### 方案1：使用屏幕调试器（推荐）

我已经为您创建了`PICOInputDebugger`，它会在PICO设备屏幕上显示调试信息：

#### 配置步骤：
1. **替换现有组件**：
   - 删除场景中的`PICODirectInputAdapter`组件
   - 添加`PICOInputDebugger`组件
   - 将VRAssemblyDebugger拖拽到Debugger字段

2. **配置设置**：
   - ✓ Create Debug UI
   - ✓ Show Controller Status
   - ✓ Show Input Status

3. **重新构建并部署**

#### 预期效果：
- 在PICO设备中会看到一个黑色半透明的调试面板
- 显示控制器状态（是否找到、是否有效）
- 实时显示按键状态（按下/释放）
- 显示功能执行结果

### 方案2：通过ADB查看日志

如果您有电脑连接PICO设备：

#### 步骤：
1. **连接PICO设备到电脑**（USB线）
2. **打开命令提示符**
3. **运行以下命令**：
```bash
adb logcat -s Unity
```
4. **在PICO设备上操作**，观察电脑上的日志输出

### 方案3：检查控制器配置

#### 可能的问题：
1. **XRController组件配置错误**
2. **控制器节点设置错误**
3. **PICO SDK版本兼容性问题**

#### 检查步骤：
1. 在Unity中选择"Right Controller"对象
2. 检查XRController组件：
   - Controller Node应该设置为"RightHand"
   - 确保组件已启用
3. 检查TrackedPoseDriver组件：
   - Device Type: Generic XR Controller
   - Pose Source: Right Pose

## 立即测试步骤

### 1. 使用PICOInputDebugger（5分钟）
1. 在场景中添加`PICOInputDebugger`组件
2. 配置Debugger引用
3. 构建并部署到PICO设备
4. 戴上头显，查看是否有调试面板显示

### 2. 观察调试信息
在PICO设备上您应该看到：
```
控制器状态：
右手控制器: 有效
设备: PICO Controller

输入状态：
扳机: 释放
主按钮: 释放
副按钮: 释放
菜单: 释放

功能信息：
14:30:25 - PICO输入调试器已启动
```

### 3. 测试按键
按下控制器按钮，观察：
- 输入状态是否从"释放"变为"按下"
- 功能信息是否显示按键响应
- 装配区域是否有移动/旋转

## 常见问题解决

### 问题1：看不到调试面板
**原因**：Canvas位置不正确
**解决**：调试面板应该出现在您前方3米处，高度2米

### 问题2：显示"未找到控制器"
**原因**：XRController组件配置问题
**解决**：
1. 检查控制器GameObject是否有XRController组件
2. 检查controllerNode设置是否正确

### 问题3：显示"控制器无效"
**原因**：PICO设备连接问题
**解决**：
1. 确保PICO控制器已开机
2. 重启PICO头显
3. 检查控制器电量

### 问题4：输入状态不变化
**原因**：输入系统配置问题
**解决**：
1. 检查Project Settings > XR Plug-in Management
2. 确保PICO插件已启用
3. 尝试重新导入PICO SDK

## 调试技巧

### 1. 分步测试
1. 先确认能看到调试面板
2. 再确认控制器状态显示正确
3. 最后测试按键响应

### 2. 使用菜单按钮
按菜单按钮可以隐藏/显示调试面板，确认输入检测是否工作

### 3. 对比测试
如果一个按钮不工作，测试其他按钮是否工作

## 预期结果

正确配置后，您应该能够：
1. ✅ 看到调试面板显示控制器状态
2. ✅ 看到按键状态实时变化
3. ✅ 按扳机键执行第一部分功能
4. ✅ 按主按钮执行第二部分功能
5. ✅ 看到装配区域移动和旋转

## 下一步

请先尝试`PICOInputDebugger`方案，然后告诉我：
1. 是否能看到调试面板？
2. 控制器状态显示什么？
3. 按键时输入状态是否变化？
4. 功能是否被执行？

这样我就能准确定位问题所在。
