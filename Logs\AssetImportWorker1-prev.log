Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker1.log
-srvPort
10633
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25124] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 258345385 [EditorId] 258345385 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [25124] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 258345385 [EditorId] 258345385 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 35.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56908
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.022679 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1073 ms
Refreshing native plugins compatible for Editor in 41.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.648 seconds
Domain Reload Profiling:
	ReloadAssembly (1648ms)
		BeginReloadAssembly (123ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1401ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (75ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (1265ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1127ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (42ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (63ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.011311 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 38.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.320 seconds
Domain Reload Profiling:
	ReloadAssembly (1320ms)
		BeginReloadAssembly (122ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (11ms)
		EndReloadAssembly (1105ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (194ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (753ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (39ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (593ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (24ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.10 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4455 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (119.9 KB). Loaded Objects now: 4936.
Memory consumption went from 180.8 MB to 180.7 MB.
Total: 2.796400 ms (FindLiveObjects: 0.281400 ms CreateObjectMapping: 0.083600 ms MarkObjects: 2.285200 ms  DeleteObjects: 0.145200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013667 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 39.31 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 3.30 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c4866893 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c486656b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c48662f0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c48661b8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000283c4863263 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000283c4882e35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000283c48824da (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000283c48823eb (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000283c4881b33 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000283bea46298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000283c26576b5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000283c2656ed3 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000283c2656b03 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000283c4882e83 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000283bb2a1640 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000283bb2a011b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000283c02bff4e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000283c2642dbb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283bea46b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c4866893 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c486656b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c48662f0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c2657945 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000283c2642ddb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283bea46b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.809 seconds
Domain Reload Profiling:
	ReloadAssembly (1810ms)
		BeginReloadAssembly (180ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1527ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (197ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (1170ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (43ms)
				BeforeProcessingInitializeOnLoad (57ms)
				ProcessInitializeOnLoadAttributes (965ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (41ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4732 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 192.2 MB to 191.0 MB.
Total: 2.525700 ms (FindLiveObjects: 0.204200 ms CreateObjectMapping: 0.082100 ms MarkObjects: 1.884300 ms  DeleteObjects: 0.354400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 14.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.15 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 98.6 MB to 97.4 MB.
Total: 2.722600 ms (FindLiveObjects: 0.236000 ms CreateObjectMapping: 0.107600 ms MarkObjects: 2.009100 ms  DeleteObjects: 0.369100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 14.22 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.15 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5203.
Memory consumption went from 98.3 MB to 97.1 MB.
Total: 2.388300 ms (FindLiveObjects: 0.207100 ms CreateObjectMapping: 0.075700 ms MarkObjects: 1.739700 ms  DeleteObjects: 0.364500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015653 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c262c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c262bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c262bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c262b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000283c2628a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000283c4897575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000283c4896c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000283c4896b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000283c4896273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000283c48f6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000283c365d4f5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000283c365ce93 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000283c365cac3 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000283c48975c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000283c2657e90 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000283c265696b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000283c265682e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000283c364933b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c48f6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c262c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c262bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c262bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c365d785 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000283c364935b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c48f6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.328 seconds
Domain Reload Profiling:
	ReloadAssembly (1329ms)
		BeginReloadAssembly (177ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (1045ms)
			LoadAssemblies (134ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (196ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (692ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (62ms)
				ProcessInitializeOnLoadAttributes (553ms)
				ProcessInitializeOnLoadMethodAttributes (21ms)
				AfterProcessingInitializeOnLoad (38ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (1.2 MB). Loaded Objects now: 5220.
Memory consumption went from 192.0 MB to 190.7 MB.
Total: 2.568800 ms (FindLiveObjects: 0.176600 ms CreateObjectMapping: 0.059800 ms MarkObjects: 1.970700 ms  DeleteObjects: 0.361000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013786 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.59 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c0ecc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c0ecbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c0ecbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c0ecb9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000283c0ec8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000283c2447575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000283c2446c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000283c2446b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000283c2446273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000283c2456298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000283c25fcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000283c25fc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000283c25fc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000283c24475c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000283c3646900 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000283c36453db (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000283c364529e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000283c25e905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c2456b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c0ecc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c0ecbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c0ecbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c25fd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000283c25e907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c2456b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.292 seconds
Domain Reload Profiling:
	ReloadAssembly (1293ms)
		BeginReloadAssembly (161ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (1031ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (185ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (686ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (65ms)
				ProcessInitializeOnLoadAttributes (548ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 4723 Unused Serialized files (Serialized files now loaded: 0)
Unloading 75 unused Assets / (1.2 MB). Loaded Objects now: 5233.
Memory consumption went from 192.0 MB to 190.7 MB.
Total: 2.387200 ms (FindLiveObjects: 0.169100 ms CreateObjectMapping: 0.061500 ms MarkObjects: 1.821400 ms  DeleteObjects: 0.334700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.28 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.14 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 66 unused Assets / (1.2 MB). Loaded Objects now: 5233.
Memory consumption went from 98.7 MB to 97.5 MB.
Total: 2.791000 ms (FindLiveObjects: 0.238400 ms CreateObjectMapping: 0.085700 ms MarkObjects: 2.102100 ms  DeleteObjects: 0.364300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017571 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c25cc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c25cbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c25cbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c25cb9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000283c25c8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000283c25d7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316e7b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffa3161ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffa3164febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000283c25d6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000283c25d6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000283c25d6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000283c25b6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff69339570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff69481b618 (Unity) CreateSerializedAssetV2
0x00007ff6947b0f55 (Unity) CreateSerializedAssetV2
0x00007ff69471f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff692c8a4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff692aab819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x00000283c0eacdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x00000283c0eac753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x00000283c0eac383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x00000283c25d75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316f46df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffa31624eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffa3164b091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x00000283c2597e90 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x00000283c259696b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x00000283c259682e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x00000283c0e6905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c25b6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff69349efad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6934a5b39 (Unity) StackWalker::ShowCallstack
0x00007ff694448df3 (Unity) GetStacktrace
0x00007ff694b1165d (Unity) DebugStringToFile
0x00007ff6925eea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000283c25cc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000283c25cbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000283c25cbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000283c0ead045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000283c0e6907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x00000283c25b6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa317b697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffa316ecdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffa316ecf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6933c1934 (Unity) scripting_method_invoke
0x00007ff6933a0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff69339b6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6934df53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff693395733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff69338b1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff693392d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6942ffa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff694300144 (Unity) LoadUserAssemblies
0x00007ff6947fc091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff69483ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff694828d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff69482a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff69483b27e (Unity) IOService::Run
0x00007ff69480dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6947d5d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6947d5d9b (Unity) RunAssetImporterV2
0x00007ff694000c48 (Unity) Application::InitializeProject
0x00007ff6944520a0 (Unity) WinMain
0x00007ff69581fbae (Unity) __scrt_common_main_seh
0x00007ffac392259d (KERNEL32) BaseThreadInitThunk
0x00007ffac4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.313 seconds
Domain Reload Profiling:
	ReloadAssembly (1313ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1041ms)
			LoadAssemblies (137ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (27ms)
			SetupLoadedEditorAssemblies (695ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (64ms)
				ProcessInitializeOnLoadAttributes (558ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4723 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (1.2 MB). Loaded Objects now: 5250.
Memory consumption went from 192.0 MB to 190.7 MB.
Total: 2.625800 ms (FindLiveObjects: 0.189900 ms CreateObjectMapping: 0.059200 ms MarkObjects: 2.013000 ms  DeleteObjects: 0.362800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
