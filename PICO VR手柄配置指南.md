# PICO VR手柄配置指南

## 当前场景分析

您的"小U和电机"场景已经包含：
✅ XR Origin (XR Rig) - 已正确配置
✅ Left Controller 和 Right Controller - 已配置射线交互
✅ VRSystemDebugger - 完整的调试系统
✅ VRAssemblyPositioner - 装配区域定位功能

## 立即配置步骤

### 1. 添加PICO输入适配器（5分钟）

在Unity中执行：
1. 在场景中创建空GameObject，命名为"PICO Input Adapter"
2. 添加`PICOVRInputAdapter`组件
3. 在Inspector中配置：
   - ✓ Enable PICO Input
   - ✓ Show Input Debug
   - 将VRSystemDebugger拖拽到Debugger字段

### 2. 验证XR Origin配置（2分钟）

检查您的XR Origin设置：
1. 选择"XR Origin (XR Rig)"对象
2. 确认Camera引用指向Main Camera
3. 确认Camera Floor Offset Object引用正确

### 3. 配置控制器交互（3分钟）

检查Left Controller和Right Controller：
1. 确认每个控制器都有XRRayInteractor组件
2. 确认LineRenderer组件用于射线可视化
3. 确认XRInteractorLineVisual组件正常工作

## PICO手柄输入映射

### 右手控制器功能：
- **扳机键 (Trigger)** → 测试位置调整功能
  - 执行`debugger.TestPositionAdjustment()`
  - 将装配区域移动到摄像机前方
  
- **主按钮 (A/X)** → 测试预览功能
  - 执行`debugger.TestPreviewFunction()`
  - 显示装配预览效果
  
- **副按钮 (B/Y)** → 测试引导功能
  - 执行`debugger.TestGuidanceFunction()`
  - 显示用户引导信息
  
- **菜单按钮 (Menu)** → 切换调试界面
  - 显示/隐藏VR系统状态信息
  - 等同于按F1键

### 左手控制器：
- 作为备用输入（当右手控制器不可用时）
- 相同的按键映射

## 测试流程

### 在Unity编辑器中测试：
1. 运行场景
2. 按F1键打开VRSystemDebugger界面
3. 点击各个测试按钮验证功能
4. 检查Console输出确认组件状态

### 在PICO设备上测试：

#### 第一步：基础VR功能验证
1. 戴上PICO头显
2. 确认能看到3D场景
3. 确认控制器射线可见且跟随手柄移动
4. 确认可以用射线指向物体

#### 第二步：测试装配区域定位（第一部分功能）
1. **按右手控制器扳机键**
2. 观察装配区域是否移动到摄像机前方
3. 检查装配区域是否朝向摄像机
4. 验证VRAssemblyPositioner.OnAssemblyStart()功能

#### 第三步：测试预览和引导功能（第二部分功能）
1. **按右手控制器主按钮(A/X)** - 测试预览功能
2. **按右手控制器副按钮(B/Y)** - 测试引导功能
3. **按右手控制器菜单按钮** - 切换调试界面

## 预期效果

### 位置调整功能（扳机键）：
- 装配区域平滑移动到摄像机前方
- 装配区域旋转朝向摄像机
- Console输出位置调整日志
- 手柄提供触觉反馈

### 预览功能（主按钮）：
- 显示装配预览效果
- 在合适位置显示预览对象
- Console输出预览创建日志

### 引导功能（副按钮）：
- 显示用户引导信息
- 引导用户到最佳观察位置
- Console输出引导开始日志

### 调试界面（菜单按钮）：
- 显示/隐藏VR系统状态
- 显示组件检查结果
- 实时更新VR状态信息

## 故障排除

### 问题1：手柄输入无响应
**检查项目：**
- XR Origin是否正确配置
- 控制器是否有XRController组件
- PICOVRInputAdapter是否正确引用VRSystemDebugger

### 问题2：射线不可见
**检查项目：**
- LineRenderer组件是否启用
- XRInteractorLineVisual组件是否配置
- 射线材质是否正确

### 问题3：位置调整功能无效果
**检查项目：**
- VRAssemblyPositioner组件是否存在
- 装配根节点是否正确设置
- 摄像机引用是否正确

### 问题4：调试界面不显示
**检查项目：**
- VRSystemDebugger组件是否启用
- Canvas是否正确创建
- UI元素是否在摄像机视野内

## 调试技巧

### 1. 使用Console日志
在PICO设备上通过ADB查看日志：
```bash
adb logcat -s Unity
```

### 2. 使用屏幕调试信息
VRSystemDebugger提供屏幕显示的调试信息

### 3. 使用Context Menu
在Inspector中右键点击组件，使用Context Menu功能：
- PICOVRInputAdapter → "显示输入映射"
- PICOVRInputAdapter → "测试所有VR功能"

## 高级配置

### 自定义输入映射：
如果需要修改按键映射，在PICOVRInputAdapter中调整：
```csharp
[SerializeField] private bool triggerTestsPositioning = true;
[SerializeField] private bool primaryButtonTestsPreview = true;
[SerializeField] private bool secondaryButtonTestsGuidance = true;
[SerializeField] private bool menuButtonToggleDebug = true;
```

### 添加更多功能：
可以扩展PICOVRInputAdapter来支持：
- 摇杆输入
- 握持按钮
- 触摸板输入
- 更复杂的手势识别

## 下一步计划

配置完成后，您可以：
1. 测试基础VR交互功能
2. 验证装配区域定位效果
3. 测试完整的装配流程
4. 添加更多VR交互功能
5. 优化用户体验

记住：PICO设备上的测试是验证VR功能的最终标准！
