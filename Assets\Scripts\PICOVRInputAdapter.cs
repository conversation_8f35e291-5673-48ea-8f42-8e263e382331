using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// PICO VR输入适配器
/// 将PICO手柄输入映射到VRSystemDebugger功能
/// </summary>
public class PICOVRInputAdapter : MonoBehaviour
{
    [Header("PICO输入映射")]
    [SerializeField] private bool enablePICOInput = true;
    [SerializeField] private bool showInputDebug = true;
    
    [Header("功能映射")]
    [SerializeField] private bool triggerTestsPositioning = true;  // 扳机键测试位置调整
    [SerializeField] private bool primaryButtonTestsPreview = true; // 主按钮测试预览
    [SerializeField] private bool secondaryButtonTestsGuidance = true; // 副按钮测试引导
    [SerializeField] private bool menuButtonToggleDebug = true; // 菜单键切换调试界面
    
    [Header("组件引用")]
    [SerializeField] private VRSystemDebugger debugger;
    
    // 输入状态跟踪
    private bool lastTriggerState = false;
    private bool lastPrimaryState = false;
    private bool lastSecondaryState = false;
    private bool lastMenuState = false;
    
    // PICO设备引用
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRController leftController;
    private XRController rightController;
#endif

    void Start()
    {
        InitializePICOInput();
        
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRSystemDebugger>();
        }
        
        Debug.Log("[PICOVRInputAdapter] PICO输入适配器已启动");
    }

    void Update()
    {
        if (!enablePICOInput) return;
        
        HandlePICOInput();
    }

    /// <summary>
    /// 初始化PICO输入系统
    /// </summary>
    private void InitializePICOInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 查找控制器
        var controllers = FindObjectsOfType<XRController>();
        foreach (var controller in controllers)
        {
            if (controller.controllerNode == XRNode.LeftHand)
            {
                leftController = controller;
                Debug.Log("[PICOVRInputAdapter] 找到左手控制器");
            }
            else if (controller.controllerNode == XRNode.RightHand)
            {
                rightController = controller;
                Debug.Log("[PICOVRInputAdapter] 找到右手控制器");
            }
        }
        
        if (leftController == null && rightController == null)
        {
            Debug.LogWarning("[PICOVRInputAdapter] 未找到VR控制器，将尝试使用通用输入");
        }
#else
        Debug.LogWarning("[PICOVRInputAdapter] XR Interaction Toolkit未安装");
#endif
    }

    /// <summary>
    /// 处理PICO输入
    /// </summary>
    private void HandlePICOInput()
    {
        // 使用右手控制器作为主要输入
        HandleControllerInput(rightController, "右手");
        
        // 如果右手控制器不可用，使用左手控制器
        if (rightController == null)
        {
            HandleControllerInput(leftController, "左手");
        }
    }

    /// <summary>
    /// 处理单个控制器输入
    /// </summary>
    private void HandleControllerInput(object controller, string handName)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var xrController = controller as XRController;
        if (xrController == null || !xrController.inputDevice.isValid) return;

        var inputDevice = xrController.inputDevice;
        
        // 获取按钮状态
        bool triggerPressed = false;
        bool primaryPressed = false;
        bool secondaryPressed = false;
        bool menuPressed = false;
        
        inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out triggerPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.primaryButton, out primaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out secondaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.menuButton, out menuPressed);
        
        // 检测按钮按下事件（边缘检测）
        if (triggerPressed && !lastTriggerState)
        {
            OnTriggerPressed(handName);
        }
        
        if (primaryPressed && !lastPrimaryState)
        {
            OnPrimaryButtonPressed(handName);
        }
        
        if (secondaryPressed && !lastSecondaryState)
        {
            OnSecondaryButtonPressed(handName);
        }
        
        if (menuPressed && !lastMenuState)
        {
            OnMenuButtonPressed(handName);
        }
        
        // 更新状态
        lastTriggerState = triggerPressed;
        lastPrimaryState = primaryPressed;
        lastSecondaryState = secondaryPressed;
        lastMenuState = menuPressed;
        
        // 显示输入调试信息
        if (showInputDebug && (triggerPressed || primaryPressed || secondaryPressed || menuPressed))
        {
            Debug.Log($"[PICOVRInputAdapter] {handName}控制器输入 - T:{triggerPressed} P:{primaryPressed} S:{secondaryPressed} M:{menuPressed}");
        }
#endif
    }

    /// <summary>
    /// 扳机键按下事件
    /// </summary>
    private void OnTriggerPressed(string handName)
    {
        if (!triggerTestsPositioning || debugger == null) return;
        
        Debug.Log($"[PICOVRInputAdapter] {handName}扳机键按下 - 执行位置调整测试");
        debugger.TestPositionAdjustment();
        
        // 提供触觉反馈
        ProvideTactileFeedback();
    }

    /// <summary>
    /// 主按钮按下事件
    /// </summary>
    private void OnPrimaryButtonPressed(string handName)
    {
        if (!primaryButtonTestsPreview || debugger == null) return;
        
        Debug.Log($"[PICOVRInputAdapter] {handName}主按钮按下 - 执行预览功能测试");
        debugger.TestPreviewFunction();
        
        ProvideTactileFeedback();
    }

    /// <summary>
    /// 副按钮按下事件
    /// </summary>
    private void OnSecondaryButtonPressed(string handName)
    {
        if (!secondaryButtonTestsGuidance || debugger == null) return;
        
        Debug.Log($"[PICOVRInputAdapter] {handName}副按钮按下 - 执行引导功能测试");
        debugger.TestGuidanceFunction();
        
        ProvideTactileFeedback();
    }

    /// <summary>
    /// 菜单按钮按下事件
    /// </summary>
    private void OnMenuButtonPressed(string handName)
    {
        if (!menuButtonToggleDebug) return;
        
        Debug.Log($"[PICOVRInputAdapter] {handName}菜单按钮按下 - 切换调试界面");
        
        // 模拟F1键按下来切换调试界面
        if (debugger != null)
        {
            // 通过反射调用私有方法
            var method = debugger.GetType().GetMethod("ToggleDebugMode", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (method != null)
            {
                method.Invoke(debugger, null);
            }
        }
        
        ProvideTactileFeedback();
    }

    /// <summary>
    /// 提供触觉反馈
    /// </summary>
    private void ProvideTactileFeedback()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 为右手控制器提供触觉反馈
        if (rightController != null && rightController.inputDevice.isValid)
        {
            rightController.inputDevice.TryGetHapticCapabilities(out var hapticCapabilities);
            if (hapticCapabilities.supportsImpulse)
            {
                rightController.inputDevice.SendHapticImpulse(0, 0.3f, 0.1f);
            }
        }
#endif
    }

    /// <summary>
    /// 显示输入映射帮助
    /// </summary>
    [ContextMenu("显示输入映射")]
    public void ShowInputMapping()
    {
        string mapping = @"
PICO VR输入映射：

右手控制器：
- 扳机键 (Trigger) → 测试位置调整功能
- 主按钮 (A/X) → 测试预览功能
- 副按钮 (B/Y) → 测试引导功能
- 菜单按钮 (Menu) → 切换调试界面

功能说明：
1. 位置调整：将装配区域移动到摄像机前方
2. 预览功能：显示装配预览效果
3. 引导功能：显示用户引导信息
4. 调试界面：显示/隐藏VR系统状态信息

注意：
- 确保PICO设备已正确连接
- 确保XR Origin已正确配置
- 确保VRSystemDebugger组件已添加到场景
";
        Debug.Log(mapping);
    }

    /// <summary>
    /// 测试所有功能
    /// </summary>
    [ContextMenu("测试所有VR功能")]
    public void TestAllVRFunctions()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICOVRInputAdapter] VRSystemDebugger未找到！");
            return;
        }

        Debug.Log("[PICOVRInputAdapter] 开始测试所有VR功能...");
        
        // 延迟执行各个测试
        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("1. 测试位置调整...");
        debugger.TestPositionAdjustment();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("2. 测试预览功能...");
        debugger.TestPreviewFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("3. 测试引导功能...");
        debugger.TestGuidanceFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("4. 重置VR功能...");
        debugger.ResetVRFunctions();
        
        Debug.Log("[PICOVRInputAdapter] 所有VR功能测试完成！");
    }

    /// <summary>
    /// 启用/禁用PICO输入
    /// </summary>
    public void SetPICOInputEnabled(bool enabled)
    {
        enablePICOInput = enabled;
        Debug.Log($"[PICOVRInputAdapter] PICO输入 {(enabled ? "已启用" : "已禁用")}");
    }

    void OnDestroy()
    {
        Debug.Log("[PICOVRInputAdapter] PICO输入适配器已销毁");
    }
}
