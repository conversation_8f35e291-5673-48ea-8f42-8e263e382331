{ "pid": "host", "ph":"M", "name": "process_name", "args": {"name": "host"} },
{ "pid": "host", "ph":"M", "name": "process_sort_index", "args": {"sort_index": 0} },
{ "pid": "host", "tid": 1, "ph":"M", "name": "thread_name", "args": {"name": ""} },
{ "pid": "host", "tid": 1,"ts": 1750743244148740,"dur": 3271601, "ph":"X", "name": "UnityLinker.exe"},
{ "pid": "host", "tid": 1,"ts": 1750743244149181,"dur": 64691, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityEngineSteps"},
{ "pid": "host", "tid": 1,"ts": 1750743244213904,"dur": 18655, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveUnityEngine"},
{ "pid": "host", "tid": 1,"ts": 1750743244232760,"dur": 41341, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\StagingArea\\Data\\Managed\\TypesInScenes.xml"},
{ "pid": "host", "tid": 1,"ts": 1750743244274115,"dur": 154746, "ph":"X", "name": "Step : Unity.Linker.Steps.InitializeEngineStrippingStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244428868,"dur": 2783, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveForEngineModuleStrippingEnabledStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244432715,"dur": 3186, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\InputSystemLink.xml"},
{ "pid": "host", "tid": 1,"ts": 1750743244436221,"dur": 13752, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveAssemblyDirectoryStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244449982,"dur": 10862, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityRootsSteps"},
{ "pid": "host", "tid": 1,"ts": 1750743244460854,"dur": 5506, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244467273,"dur": 1994, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244469269,"dur": 2800, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244472929,"dur": 1468, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244474654,"dur": 1130, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244475793,"dur": 1163, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityLoadReferencesStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244478627,"dur": 4343, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveFromDescriptorsStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244483190,"dur": 4261, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1750743244488767,"dur": 9612, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.UnityBlacklistStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244498381,"dur": 4723, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: mscorlib Resource: mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1750743244503111,"dur": 263264, "ph":"X", "name": "Step : Mono.Linker.Steps.DynamicDependencyLookupStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244769052,"dur": 11778, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1750743244781167,"dur": 95337, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromPreserveAttribute"},
{ "pid": "host", "tid": 1,"ts": 1750743244876522,"dur": 8322, "ph":"X", "name": "Step : Unity.Linker.Steps.EngineStrippingAnnotationStep"},
{ "pid": "host", "tid": 1,"ts": 1750743244884855,"dur": 166768, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityTypeMapStep"},
{ "pid": "host", "tid": 1,"ts": 1750743245051861,"dur": 97014, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeMarkAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1750743245148887,"dur": 12909, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveSecurityStep"},
{ "pid": "host", "tid": 1,"ts": 1750743245161809,"dur": 3639, "ph":"X", "name": "Step : Unity.Linker.Steps.RemoveSecurityFromCopyAssemblies"},
{ "pid": "host", "tid": 1,"ts": 1750743245165461,"dur": 9900, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveFeaturesStep"},
{ "pid": "host", "tid": 1,"ts": 1750743245175377,"dur": 11582, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveUnreachableBlocksStep"},
{ "pid": "host", "tid": 1,"ts": 1750743245186970,"dur": 1595673, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityMarkStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246783145,"dur": 5672, "ph":"X", "name": "Step : Mono.Linker.Steps.ProcessWarningsStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246788824,"dur": 79844, "ph":"X", "name": "Step : Unity.Linker.Steps.UnitySweepStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246868679,"dur": 2048, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityCodeRewriterStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246870734,"dur": 2556, "ph":"X", "name": "Step : Mono.Linker.Steps.CleanStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246873298,"dur": 2061, "ph":"X", "name": "Step : Unity.Linker.Steps.StubifyStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246875364,"dur": 2519, "ph":"X", "name": "Step : Unity.Linker.Steps.AddUnresolvedStubsStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246877888,"dur": 2679, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeOutputAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1750743246881309,"dur": 489030, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityOutputStep"},
{ "pid": "host", "tid": 1,"ts": 1750743247370348,"dur": 21575, "ph":"X", "name": "Step : Unity.Linker.Steps.LinkerToEditorDataGenerationStep"},
{ "pid": "host", "tid": 1,"ts": 1750743247397261,"dur": 23079, "ph":"X", "name": "Analytics"},
{ "pid": "host", "tid": 0, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": 0,"ts": 1750743244250000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC during background GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244455000,"dur": 4000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244511000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244596000,"dur": 8000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244659000,"dur": 19000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244805000,"dur": 25000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743244921000,"dur": 33000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245067000,"dur": 19000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245131000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245281000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245303000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245346000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245365000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245399000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245431000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245452000,"dur": 4000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245587000,"dur": 12000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245717000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743245864000,"dur": 13000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743246076000,"dur": 9000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743246521000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743246701000,"dur": 19000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743247028000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743247100000,"dur": 21000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743247294000,"dur": 12000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1750743247347000,"dur": 20000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -1,"ts": 1750743244337000,"dur": 3000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743244546000,"dur": 16000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743244620000,"dur": 24000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743244695000,"dur": 54000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743244851000,"dur": 23000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743244986000,"dur": 54000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245099000,"dur": 23000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245142000,"dur": 2000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245324000,"dur": 6000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245381000,"dur": 6000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245414000,"dur": 1000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245661000,"dur": 10000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245785000,"dur": 36000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743245934000,"dur": 34000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246136000,"dur": 14000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246226000,"dur": 25000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246370000,"dur": 16000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246439000,"dur": 22000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246604000,"dur": 44000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743246810000,"dur": 41000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743247174000,"dur": 16000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1750743247232000,"dur": 25000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -2, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -2,"ts": 1750743244195000,"dur": 2000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1750743244246000,"dur": 6000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1750743244546000,"dur": 35000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1750743244695000,"dur": 86000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1750743245934000,"dur": 105000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
