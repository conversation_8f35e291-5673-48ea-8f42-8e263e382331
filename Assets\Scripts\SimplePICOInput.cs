using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// 简化的PICO输入控制器
/// 专门用于测试VRSystemDebugger功能
/// </summary>
public class SimplePICOInput : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRSystemDebugger debugger;
    
    [Header("输入设置")]
    [SerializeField] private bool enableVRInput = true;
    [SerializeField] private bool enableKeyboardFallback = true;
    [SerializeField] private bool showInputDebug = true;
    
    [Header("按键映射")]
    [SerializeField] private KeyCode fallbackPositionKey = KeyCode.T;
    [SerializeField] private KeyCode fallbackPreviewKey = KeyCode.P;
    [SerializeField] private KeyCode fallbackGuidanceKey = KeyCode.G;
    [SerializeField] private KeyCode fallbackDebugKey = KeyCode.F1;

    void Start()
    {
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRSystemDebugger>();
        }
        
        Debug.Log("[SimplePICOInput] 简化PICO输入控制器已启动");
        ShowInputMapping();
    }

    void Update()
    {
        if (enableVRInput)
        {
            HandleVRInput();
        }
        
        if (enableKeyboardFallback)
        {
            HandleKeyboardInput();
        }
    }

    /// <summary>
    /// 处理VR输入
    /// </summary>
    private void HandleVRInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 查找右手控制器
        var controllers = FindObjectsOfType<XRController>();
        foreach (var controller in controllers)
        {
            if (controller.controllerNode == XRNode.RightHand && controller.inputDevice.isValid)
            {
                CheckControllerInput(controller, "右手");
                break;
            }
        }
        
        // 如果没有右手控制器，使用左手控制器
        if (controllers.Length > 0)
        {
            foreach (var controller in controllers)
            {
                if (controller.controllerNode == XRNode.LeftHand && controller.inputDevice.isValid)
                {
                    CheckControllerInput(controller, "左手");
                    break;
                }
            }
        }
#endif
    }

    /// <summary>
    /// 检查控制器输入
    /// </summary>
    private void CheckControllerInput(object controllerObj, string handName)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var controller = controllerObj as XRController;
        if (controller == null) return;

        var inputDevice = controller.inputDevice;
        
        // 检查扳机键
        if (inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out bool triggerPressed) && triggerPressed)
        {
            TestPositionAdjustment(handName);
        }
        
        // 检查主按钮
        if (inputDevice.TryGetFeatureValue(CommonUsages.primaryButton, out bool primaryPressed) && primaryPressed)
        {
            TestPreviewFunction(handName);
        }
        
        // 检查副按钮
        if (inputDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out bool secondaryPressed) && secondaryPressed)
        {
            TestGuidanceFunction(handName);
        }
        
        // 检查菜单按钮
        if (inputDevice.TryGetFeatureValue(CommonUsages.menuButton, out bool menuPressed) && menuPressed)
        {
            ToggleDebugInterface(handName);
        }
        
        // 显示输入调试信息
        if (showInputDebug && (triggerPressed || primaryPressed || secondaryPressed || menuPressed))
        {
            Debug.Log($"[SimplePICOInput] {handName}控制器输入 - T:{triggerPressed} P:{primaryPressed} S:{secondaryPressed} M:{menuPressed}");
        }
#endif
    }

    /// <summary>
    /// 处理键盘输入（备用）
    /// </summary>
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(fallbackPositionKey))
        {
            TestPositionAdjustment("键盘");
        }
        
        if (Input.GetKeyDown(fallbackPreviewKey))
        {
            TestPreviewFunction("键盘");
        }
        
        if (Input.GetKeyDown(fallbackGuidanceKey))
        {
            TestGuidanceFunction("键盘");
        }
        
        if (Input.GetKeyDown(fallbackDebugKey))
        {
            ToggleDebugInterface("键盘");
        }
    }

    /// <summary>
    /// 测试位置调整功能
    /// </summary>
    private void TestPositionAdjustment(string inputSource)
    {
        if (debugger == null)
        {
            Debug.LogError("[SimplePICOInput] VRSystemDebugger未找到！");
            return;
        }
        
        Debug.Log($"[SimplePICOInput] {inputSource}输入 - 执行位置调整测试");
        debugger.TestPositionAdjustment();
        
        // 延迟防止重复触发
        StartCoroutine(InputCooldown(0.5f));
    }

    /// <summary>
    /// 测试预览功能
    /// </summary>
    private void TestPreviewFunction(string inputSource)
    {
        if (debugger == null) return;
        
        Debug.Log($"[SimplePICOInput] {inputSource}输入 - 执行预览功能测试");
        debugger.TestPreviewFunction();
        
        StartCoroutine(InputCooldown(0.5f));
    }

    /// <summary>
    /// 测试引导功能
    /// </summary>
    private void TestGuidanceFunction(string inputSource)
    {
        if (debugger == null) return;
        
        Debug.Log($"[SimplePICOInput] {inputSource}输入 - 执行引导功能测试");
        debugger.TestGuidanceFunction();
        
        StartCoroutine(InputCooldown(0.5f));
    }

    /// <summary>
    /// 切换调试界面
    /// </summary>
    private void ToggleDebugInterface(string inputSource)
    {
        if (debugger == null) return;
        
        Debug.Log($"[SimplePICOInput] {inputSource}输入 - 切换调试界面");
        
        // 通过反射调用私有方法
        var method = debugger.GetType().GetMethod("ToggleDebugMode", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        method?.Invoke(debugger, null);
        
        StartCoroutine(InputCooldown(0.5f));
    }

    /// <summary>
    /// 输入冷却时间
    /// </summary>
    private System.Collections.IEnumerator InputCooldown(float duration)
    {
        enableVRInput = false;
        enableKeyboardFallback = false;
        
        yield return new WaitForSeconds(duration);
        
        enableVRInput = true;
        enableKeyboardFallback = true;
    }

    /// <summary>
    /// 显示输入映射
    /// </summary>
    [ContextMenu("显示输入映射")]
    public void ShowInputMapping()
    {
        string mapping = @"
SimplePICOInput 输入映射：

VR控制器输入：
- 扳机键 (Trigger) → 测试位置调整功能
- 主按钮 (A/X) → 测试预览功能  
- 副按钮 (B/Y) → 测试引导功能
- 菜单按钮 (Menu) → 切换调试界面

键盘备用输入：
- T键 → 测试位置调整功能
- P键 → 测试预览功能
- G键 → 测试引导功能
- F1键 → 切换调试界面

使用说明：
1. 在PICO设备上使用控制器按钮
2. 在Unity编辑器中使用键盘按键
3. 观察Console输出和场景变化
4. 每次输入后有0.5秒冷却时间防止重复触发
";
        Debug.Log(mapping);
    }

    /// <summary>
    /// 手动测试所有功能
    /// </summary>
    [ContextMenu("测试所有功能")]
    public void TestAllFunctions()
    {
        if (debugger == null)
        {
            Debug.LogError("[SimplePICOInput] VRSystemDebugger未找到！");
            return;
        }

        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("[SimplePICOInput] 开始自动测试序列...");
        
        Debug.Log("1. 测试位置调整...");
        debugger.TestPositionAdjustment();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("2. 测试预览功能...");
        debugger.TestPreviewFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("3. 测试引导功能...");
        debugger.TestGuidanceFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("4. 重置VR功能...");
        debugger.ResetVRFunctions();
        
        Debug.Log("[SimplePICOInput] 自动测试序列完成！");
    }

    /// <summary>
    /// 检查VR系统状态
    /// </summary>
    [ContextMenu("检查VR状态")]
    public void CheckVRStatus()
    {
        if (debugger != null)
        {
            debugger.CheckVRSystemStatus();
        }
        else
        {
            Debug.LogError("[SimplePICOInput] VRSystemDebugger未找到！");
        }
    }
}
