<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="AlignmentTest" preserve="nothing"/>
		<type fullname="AssemblyAnimationManager" preserve="nothing"/>
		<type fullname="AssemblyPart" preserve="nothing"/>
		<type fullname="Neo4jAssemblyController" preserve="nothing"/>
		<type fullname="Neo4jAssemblyUI" preserve="nothing"/>
		<type fullname="Neo4jConnector" preserve="nothing"/>
		<type fullname="VRAssemblyDebugger" preserve="nothing"/>
		<type fullname="VRAssemblyManager" preserve="nothing"/>
		<type fullname="VRAssemblyOrientationHelper" preserve="nothing"/>
		<type fullname="VRAssemblyPositioner" preserve="nothing"/>
		<type fullname="VRAssemblyPreview" preserve="nothing"/>
		<type fullname="VRInputManager" preserve="nothing"/>
		<type fullname="VRSimulator" preserve="nothing"/>
		<type fullname="VRSystemDebugger" preserve="nothing"/>
		<type fullname="VRUserGuidance" preserve="nothing"/>
	</assembly>
	<assembly fullname="PICO.Platform">
		<type fullname="Unity.XR.PXR.PXR_PlatformSetting" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputSettings" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSystemObject" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.RemoteInputPlayerConnection" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.XR.TrackedPoseDriver" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.TMP_FontAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_Settings" preserve="nothing"/>
		<type fullname="TMPro.TMP_SpriteAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_StyleSheet" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.XR.CoreUtils">
		<type fullname="Unity.XR.CoreUtils.XROrigin" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.XR.Interaction.Toolkit">
		<type fullname="UnityEngine.XR.Interaction.Toolkit.ActionBasedController" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.Inputs.InputActionManager" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation.XRDeviceSimulatorSettings" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.InteractionLayerSettings" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRInteractionManager" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRInteractorLineVisual" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRRayInteractor" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRSimpleInteractable" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.XR.Management">
		<type fullname="UnityEngine.XR.Management.XRGeneralSettings" preserve="nothing"/>
		<type fullname="UnityEngine.XR.Management.XRManagerSettings" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.XR.PICO">
		<type fullname="PXR_ControllerPower" preserve="nothing"/>
		<type fullname="PXR_Hand" preserve="nothing"/>
		<type fullname="PXR_HandPosePreview" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_ControllerAnimator" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_ControllerG3Animator" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_ControllerLoader" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_ControllerWithHandAnimator" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_HandPoseGenerator" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_Loader" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_ProjectSetting" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_Settings" preserve="nothing"/>
		<type fullname="Unity.XR.PXR.PXR_SpatialAnchor" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.Animations.AnimatorController" preserve="nothing"/>
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PhysicsManager" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.UnityConnectSettings" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.AnimationClip" preserve="nothing"/>
		<type fullname="UnityEngine.Animator" preserve="nothing"/>
		<type fullname="UnityEngine.AnimatorOverrideController" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.Avatar" preserve="nothing"/>
		<type fullname="UnityEngine.AvatarMask" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.BoxCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Canvas" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Collider" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.Light" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LineRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MeshCollider" preserve="nothing"/>
		<type fullname="UnityEngine.MeshFilter" preserve="nothing"/>
		<type fullname="UnityEngine.MeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Motion" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.RectTransform" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.SortingGroup" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rigidbody" preserve="nothing"/>
		<type fullname="UnityEngine.RuntimeAnimatorController" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.SphereCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Slider" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="nothing"/>
	</assembly>
</linker>
