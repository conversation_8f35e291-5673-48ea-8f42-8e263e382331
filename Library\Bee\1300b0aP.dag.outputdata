{"ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Cinemachine.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Cinemachine.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Cinemachine.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Interaction.Toolkit.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Interaction.Toolkit.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Interaction.Toolkit.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.PICO.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Unity.XR.PICO.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Unity.XR.PICO.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/PICO.Platform.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/PICO.Platform.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/PICO.Platform.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/PICO.TobSupport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/PICO.TobSupport.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/PICO.TobSupport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.Example.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.Example.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Pico.Spatializer.Example.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm"}]}}